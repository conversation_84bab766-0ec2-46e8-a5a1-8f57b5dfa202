package com.digiwin.escloud.aiocmdb.asset.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetAttribute;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.service.AssetRelatedMapService;
import com.digiwin.escloud.aiocmdb.etl.dao.EtlMapper;
import com.digiwin.escloud.aiocmdb.etl.model.EtlModelField;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.model.model.ModelFieldGroup;
import com.digiwin.escloud.aiocmdb.util.CommonUtils;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aiocmdb.asset.service.impl.AssetServiceImpl.ASSET_ID;

@Service
@Slf4j
public class AssetAutomaticallyEstablishedService {

    @Autowired
    private BigDataUtil bigDataUtil;
    @Autowired
    private EtlMapper etlMapper;
    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private ModelMapper modelMapper;
    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;

    @Autowired
    private AssetRelatedMapService assetRelatedMapService;

    // 1. 将所有硬编码的键和值定义为常量，提高可维护性
    private static final String KEY_AIOPS_ITEM_ID = "aiopsItemId";
    private static final String KEY_AIOPS_ITEM = "aiopsItem";
    private static final String KEY_USE_STATUS = "useStatus";
    private static final String KEY_EID = "eid";
    private static final String STATUS_SCRAPPED = "Scrapped";

    /**
     * 批量保存数据到StarRocks和HBase（支持更新模式）
     *
     * @param dataRows  数据行列表，每行是{"字段名":"字段值"}的映射
     * @param modelCode 模型代码
     * @param database  数据库名称
     * @return 保存结果
     */
    public BaseResponse batchSaveToStarRocksAndHBaseWithUpdate(List<LinkedHashMap<String, Object>> dataRows,
                                                               String modelCode, String database, Long eid) {
        try {
            if (CollectionUtils.isEmpty(dataRows)) {
                log.warn("数据行列表为空，跳过保存操作");
                return null;
            }
            // 1. 保存到StarRocks
            saveToStarRocksWithData(dataRows, modelCode, database,eid);

            // 2. 保存到HBase（支持更新模式）
            saveToHBaseWithStructureAndUpdate(dataRows, modelCode, eid);

            log.info("成功批量保存{}条数据到StarRocks和HBase", dataRows.size());
            return BaseResponse.ok(dataRows.size());

        } catch (Exception e) {
            log.error("批量保存数据失败", e);
            return BaseResponse.error(e);
        }
    }

    /**
     * 保存数据到StarRocks
     *
     * @param modelCode 模型代码
     * @param database  数据库名称
     * @param eid
     */
    private void saveToStarRocksWithData(List<LinkedHashMap<String, Object>> dataRowList,
                                         String modelCode, String database, Long eid) throws Exception {
        // 调用bigDataUtil的srStreamLoadThrowsException执行保存
        AssetAttribute assetCodeGenParam = new AssetAttribute();
        AssetAttribute.SrData srData = new AssetAttribute.SrData();
        srData.setTableName(modelCode);
        assetCodeGenParam.setSrData(srData);
        for (LinkedHashMap<String, Object> dataRow : dataRowList) {
            if (!dataRow.containsKey(ASSET_ID)||dataRow.get(ASSET_ID) == null){

                dataRow.put(ASSET_ID, SnowFlake.getInstance().newId());
                // 获取资产编码
                assetRelatedMapService.getNewAssetCode(modelCode, String.valueOf(eid), assetCodeGenParam)
                        .ifPresent(assetCode -> {
                            dataRow.put("assetCode", assetCode);
                            log.info("成功获取资产编码: {}", assetCode);
                        });
            }
        }
        StarRocksEntity starRocksEntity = bigDataUtil.getStarRocksEntity(dataRowList);

        // 设置其他字段
        starRocksEntity.setDatabase(database);
        starRocksEntity.setTable(modelCode);

        bigDataUtil.srStreamLoadThrowsException(starRocksEntity);

        log.info("成功保存{}条数据到StarRocks表: {}.{}", dataRowList.size(), database, modelCode);


    }

    /**
     * 保存数据到HBase，根据etl_engine表的sinkFieldsJson配置生成结构（支持更新模式）
     *
     * @param dataRows  数据行列表
     * @param modelCode 模型代码
     */
    private void saveToHBaseWithStructureAndUpdate(List<LinkedHashMap<String, Object>> dataRows, String modelCode
            , Long eid) throws Exception {
        // 1. 获取sinkFieldsJson配置
        String sinkFieldsJson = etlMapper.getSinkFieldsJson("starrocks", "default", modelCode);
        if (StringUtils.isEmpty(sinkFieldsJson)) {
            log.warn("未找到模型{}的sinkFieldsJson配置，跳过HBase保存", modelCode);
            return;
        }

        // 2. 解析sinkFieldsJson为EtlModelField列表
        List<EtlModelField> etlModelFields = JSON.parseArray(sinkFieldsJson, EtlModelField.class);
        if (CollectionUtils.isEmpty(etlModelFields)) {
            log.warn("模型{}的sinkFieldsJson配置为空，跳过HBase保存", modelCode);
            return;
        }

        // 3. 获取动态字段分组
        List<ModelFieldGroup> fieldGroups = getModelFieldGroups(modelCode);

        // 4. 为每行数据生成HBase结构并保存
        for (LinkedHashMap<String, Object> dataRow : dataRows) {
            // 获取ID字段作为rowKey
            String id = String.valueOf(dataRow.get("assetId"));
            try {

                if (StringUtils.isEmpty(id)) {
                    log.warn("数据行中未找到ID字段，跳过该行数据: {}", dataRow);
                    continue;
                }

                // 检查HBase中是否已存在该数据
                Object existingData = commonUtils.getMrDetail(modelCode, id, eid);

                if (existingData != null) {
                    // 数据存在，执行更新操作
                    updateExistingHBaseData(dataRow, etlModelFields, modelCode, id, eid);
                } else {
                    // 数据不存在，执行新增操作
                    String hbaseJsonStructure = generateDynamicHBaseJsonStructure(dataRow, etlModelFields, fieldGroups);
                    commonUtils.saveMrDetail(modelCode,eid, id, hbaseJsonStructure);
                }

                log.debug("成功处理数据到HBase，modelCode: {}, id: {}", modelCode, id);

            } catch (Exception e) {
                log.error("处理数据到HBase失败，数据行: {}", dataRow, e);
                //TODO: 保存可能是没有hbase 尝试保存一下数据触发创建hbase表 后续看看有没有特殊处理
                String hbaseJsonStructure = generateDynamicHBaseJsonStructure(dataRow, etlModelFields, fieldGroups);
                commonUtils.saveMrDetail(modelCode, id, hbaseJsonStructure);
                // 继续处理下一行，不中断整个批量操作
            }
        }

        log.info("成功处理{}条数据到HBase", dataRows.size());
    }

    /**
     * 获取模型的字段分组
     *
     * @param modelCode 模型代码
     * @return 字段分组列表
     */
    private List<ModelFieldGroup> getModelFieldGroups(String modelCode) {
        try {
            long sid = RequestUtil.getHeaderSid();
            return modelMapper.getModelFieldGroupList(sid, modelCode);
        } catch (Exception e) {
            log.error("获取模型字段分组失败，modelCode: {}", modelCode, e);
            return new ArrayList<>();
        }
    }


    /**
     * 根据etlModelFields配置和动态字段分组生成HBase的JSON结构
     *
     * @param dataRow        数据行
     * @param etlModelFields ETL模型字段配置
     * @param fieldGroups    字段分组列表
     * @return HBase JSON结构字符串
     */
    private String generateDynamicHBaseJsonStructure(LinkedHashMap<String, Object> dataRow,
                                                     List<EtlModelField> etlModelFields,
                                                     List<ModelFieldGroup> fieldGroups) {
        // 创建根JSON对象
        JSONObject rootJson = new JSONObject();

        // 设置固定字段
        String currentTime = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        rootJson.put("maintenanceTime", currentTime);
        rootJson.put("updateTime", currentTime);

        // 创建field对象
        JSONObject fieldJson = new JSONObject();

        // 根据字段分组动态创建分组对象
        Map<String, JSONObject> groupJsonMap = new HashMap<>();
        for (ModelFieldGroup fieldGroup : fieldGroups) {
            String groupCode = fieldGroup.getModelFieldGroupCode();
            groupJsonMap.put(groupCode, new JSONObject());
        }

        // 如果没有字段分组，创建默认的DataContent分组
        if (groupJsonMap.isEmpty()) {
            groupJsonMap.put("DataContent", new JSONObject());
        }

        // 根据etlModelFields配置分配字段到对应的组
        for (EtlModelField etlField : etlModelFields) {
            String fieldCode = etlField.getFieldCode();
            String valuePath = etlField.getValuePath();

            // 从数据行中获取字段值
            Object fieldValue = dataRow.get(fieldCode);
            if (fieldValue == null) {
                fieldValue = StringUtils.isEmpty(etlField.getDefaultValue()) ? "" : etlField.getDefaultValue();
            }

            // 解析valuePath确定字段应该放在哪个组中
            String[] pathParts = valuePath.split("\\.");
            if (pathParts.length >= 2) {
                String groupName = pathParts[0];
                String fieldName = pathParts[pathParts.length - 1];

                // 如果分组存在，将字段添加到对应分组
                if (groupJsonMap.containsKey(groupName)) {
                    groupJsonMap.get(groupName).put(fieldName, fieldValue);
                } else {
                    // 如果分组不存在，添加到DataContent分组
                    if (!groupJsonMap.containsKey("DataContent")) {
                        groupJsonMap.put("DataContent", new JSONObject());
                    }
                    groupJsonMap.get("DataContent").put(fieldName, fieldValue);
                    log.debug("字段{}的分组{}不存在，添加到DataContent分组", fieldCode, groupName);
                }
            } else {
                // 如果valuePath格式不符合预期，默认放到DataContent中
                if (!groupJsonMap.containsKey("DataContent")) {
                    groupJsonMap.put("DataContent", new JSONObject());
                }
                groupJsonMap.get("DataContent").put(fieldCode, fieldValue);
                log.debug("字段{}的valuePath格式不符合预期: {}，默认放到DataContent中", fieldCode, valuePath);
            }
        }

        // 将所有分组添加到field对象中
        fieldJson.putAll(groupJsonMap);

        rootJson.put("field", fieldJson);

        return rootJson.toJSONString();
    }

    /**
     * 更新HBase中已存在的数据
     *
     * @param dataRow        数据行
     * @param etlModelFields ETL模型字段配置
     * @param modelCode      模型代码
     * @param id             数据ID
     */
    private void updateExistingHBaseData(LinkedHashMap<String, Object> dataRow,
                                         List<EtlModelField> etlModelFields,
                                         String modelCode, String id, Long eid) {
        try {
            // 准备字段更新映射
            Map<String, Object> fieldUpdates = new HashMap<>();

            for (EtlModelField etlField : etlModelFields) {
                String fieldCode = etlField.getFieldCode();
                String valuePath = etlField.getValuePath();

                // 从数据行中获取字段值
                Object fieldValue = dataRow.get(fieldCode);
                if (fieldValue != null) {
                    // 构建完整的字段路径
                    String fieldPath = "field." + valuePath;
                    fieldUpdates.put(fieldPath, fieldValue);
                }
            }

            // 更新updateTime
            String currentTime = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            fieldUpdates.put("updateTime", currentTime);

            // 批量更新HBase中的字段
            HashMap<String, Object> result = commonUtils.updateMrDetailFields(modelCode, eid, id, fieldUpdates);
            if (result != null) {
                log.debug("成功更新HBase已存在数据，modelCode: {}, id: {}, 更新字段数: {}",
                        modelCode, id, fieldUpdates.size());
            } else {
                log.warn("成功更新HBase已存在数据，modelCode: {}, id: {}", modelCode, id);
            }

        } catch (Exception e) {
            log.error("更新HBase已存在数据异常，modelCode: {}, id: {}", modelCode, id, e);
        }
    }

    /**
     * 暂时用不到
     *
     * @param dataRows
     * @param modelCode
     * @param database
     * @param modifyInstanceStatus
     * @return
     */
    public BaseResponse modifyAssetStatus(List<LinkedHashMap<String, Object>> dataRows, String modelCode, String database, Boolean modifyInstanceStatus) {
        if (CollectionUtils.isEmpty(dataRows)) {
            return BaseResponse.ok("No data rows to process.");
        }

        if (dataRows.size() > 1) {
            return BaseResponse.ok("Not support.");
        }

//        batchSaveToStarRocksAndHBaseWithUpdate(dataRows, modelCode, database);

        if (Boolean.TRUE.equals(modifyInstanceStatus)) {
            List<AiopsItemContextDTO> itemsToInvalidate = dataRows.stream()
                    // 过滤掉 null 的 map，并筛选出状态为 "Scrapped" 的行
                    .filter(row -> row != null && STATUS_SCRAPPED.equals(getMapValueAsString(row, KEY_USE_STATUS)))
                    // 将符合条件的 map 转换为 AiopsItemContextDTO
                    .map(row -> {
                        AiopsItemContextDTO dto = new AiopsItemContextDTO();
                        dto.setAiopsItemId(getMapValueAsString(row, KEY_AIOPS_ITEM_ID));
                        dto.setAiopsItem(getMapValueAsString(row, KEY_AIOPS_ITEM));
                        return dto;
                    })
                    .collect(Collectors.toList());

            //仅当有需要失效的资产时，才执行远程调用
            if (!itemsToInvalidate.isEmpty()) {
                Object eidObject = dataRows.get(0).get(KEY_EID);
                Long eid = LongUtil.objectToLong(eidObject);

                aioItmsFeignClient.modifyAiopsInstanceAuth(
                        eid,
                        AiopsAuthStatus.INVALID,
                        true, // modifyInstanceStatus 在这里必然是 true
                        itemsToInvalidate // 传入包含所有待处理项的列表
                );
            }
        }

        return BaseResponse.ok();
    }

    /**
     * 辅助方法：从 Map 中安全地获取值并转换为字符串。
     *
     * @param map the map
     * @param key the key
     */
    private String getMapValueAsString(java.util.Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value == null ? null : value.toString();
    }
}
