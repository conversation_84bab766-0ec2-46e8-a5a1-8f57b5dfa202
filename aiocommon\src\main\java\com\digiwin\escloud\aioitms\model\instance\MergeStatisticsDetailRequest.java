package com.digiwin.escloud.aioitms.model.instance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@ApiModel("合并统计明细请求条件")
@Data
public class MergeStatisticsDetailRequest extends DeviceListBaseRequest {
    @ApiModelProperty("运维商运维模组类别列表代号")
    private String classCode;
    @ApiModelProperty("设备名称或Id")
    private String deviceNameOrId;
    @ApiModelProperty("Ip地址")
    private String ipAddress;
    @ApiModelProperty("放置点")
    private String placementPoint;
    @ApiModelProperty("设备类型列表")
    private List<String> aiopsItemList;
    @ApiModelProperty("实例id列表")
    private List<String> instanceIdList;

    private List<Long> aiIdList;
    // 查询不在aiIdList中的实例
    private List<Long> notInAiIdList;

    private int pageNum;
    private int pageSize;
}
